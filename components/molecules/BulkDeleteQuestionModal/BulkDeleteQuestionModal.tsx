'use client';

import React, { useState } from 'react';
import { bulkRemoveQuestionsAction } from '@/actions/worksheet.action';
import { Loader2, <PERSON>ert<PERSON><PERSON><PERSON>, Trash, X } from 'lucide-react';
import { Question } from '@/components/molecules/QuestionListingView/QuestionListingView';

export interface BulkDeleteQuestionModalProps {
  isOpen: boolean;
  onClose: () => void;
  questions: Question[]; // All questions for reference
  selectedQuestionIds: string[]; // IDs of questions to delete
  worksheetId: string;
  onSuccess?: () => void;
}

export const BulkDeleteQuestionModal: React.FC<BulkDeleteQuestionModalProps> = ({
  isOpen,
  onClose,
  questions,
  selectedQuestionIds,
  worksheetId,
  onSuccess,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Get selected questions for preview
  const selectedQuestions = questions.filter(q => q.id && selectedQuestionIds.includes(q.id));

  // Handle bulk delete confirmation
  const handleBulkDelete = async () => {
    if (selectedQuestionIds.length === 0) {
      setError('No questions selected for deletion.');
      return;
    }

    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await bulkRemoveQuestionsAction(worksheetId, {
        questionIds: selectedQuestionIds
      });

      if (response.status === 'success') {
        const successCount = response.data?.successCount || selectedQuestionIds.length;
        setSuccess(`Successfully deleted ${successCount} question${successCount !== 1 ? 's' : ''}!`);
        // Call onSuccess callback after a short delay
        setTimeout(() => {
          if (onSuccess) {
            onSuccess();
          }
          onClose(); // Close modal after success and delay
        }, 1500);
      } else {
        // Handle errors
        const errorMessage = Array.isArray(response.message) 
          ? response.message.join(', ') 
          : response.message || 'Failed to delete questions.';
        setError(errorMessage);
      }
    } catch (error: any) {
      console.error('Error bulk deleting questions:', error);
      setError(error.message || 'An unexpected error occurred while deleting questions.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Truncate content for preview
  const truncateContent = (content: string, maxLength: number = 60) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  if (!isOpen) return null;

  return (
    <>
      <style jsx>{`
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }
        .animate-fadeIn {
          animation: fadeIn 0.2s ease-out;
        }
      `}</style>
      <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4 animate-fadeIn" style={{ zIndex: 9999 }}>
        <div className="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden transform transition-all duration-300 scale-100">
        {/* Header */}
        <div className="bg-red-600 text-white p-4 flex justify-between items-center">
          <div className="flex items-center gap-3">
            <Trash className="w-5 h-5 flex-shrink-0" />
            <div>
              <h3 className="text-lg font-semibold">Delete Multiple Questions</h3>
              <p className="text-red-100 text-sm">
                {selectedQuestionIds.length} question{selectedQuestionIds.length !== 1 ? 's' : ''} selected
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            disabled={isSubmitting}
            className="rounded-full p-1.5 bg-white/20 hover:bg-white/30 transition-colors duration-200 disabled:opacity-50"
            aria-label="Close"
          >
            <X size={16} className="text-white" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto">
          {/* Questions Preview */}
          <div className="mb-6 max-h-60 overflow-y-auto">
            <h4 className="text-sm font-medium text-gray-700 mb-3">Questions to be deleted:</h4>
            <div className="space-y-2">
              {selectedQuestions.map((question, index) => (
                <div key={question.id} className="p-3 bg-gray-50 rounded-lg border border-gray-200 text-sm">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium text-gray-600">
                      Question {questions.findIndex(q => q.id === question.id) + 1}:
                    </span>
                    <span className="text-xs px-2 py-1 bg-green-100 text-green-700 rounded-full font-medium capitalize">
                      {question.type.replace('_', ' ')}
                    </span>
                  </div>
                  <div className="text-gray-800">
                    {truncateContent(question.content)}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Warning Message */}
          <div className="flex items-start space-x-3 mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <AlertCircle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-sm font-medium text-red-800">
                Are you sure you want to delete these {selectedQuestionIds.length} question{selectedQuestionIds.length !== 1 ? 's' : ''}?
              </p>
              <p className="text-sm text-red-700 mt-1">
                This action cannot be undone. All selected questions will be permanently removed from the worksheet.
              </p>
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <div className="flex items-center">
                <AlertCircle className="w-4 h-4 text-red-600 mr-2" />
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          )}

          {/* Success Message */}
          {success && (
            <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md">
              <div className="flex items-center">
                <div className="w-4 h-4 bg-green-600 rounded-full mr-2 flex items-center justify-center">
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                </div>
                <p className="text-sm text-green-700">{success}</p>
              </div>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
          <button
            onClick={onClose}
            disabled={isSubmitting}
            className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 transition-colors font-medium"
          >
            Cancel
          </button>
          <button
            onClick={handleBulkDelete}
            disabled={isSubmitting || success !== null}
            className="px-4 py-2 text-white bg-red-600 rounded-lg hover:bg-red-700 disabled:opacity-50 flex items-center transition-colors font-medium shadow-sm"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="animate-spin mr-2" size={16} />
                Deleting...
              </>
            ) : (
              <>
                <Trash className="mr-2" size={16} />
                Delete {selectedQuestionIds.length} Question{selectedQuestionIds.length !== 1 ? 's' : ''}
              </>
            )}
          </button>
        </div>
      </div>
    </>
  );
};
